# 🩺 SOAP Note App

Real-time Patient-Doctor Conversation Analysis with SOAP Note Generation

## Features
- **Real-time Voice Recording**: Listen to doctor-patient conversations
- **Speaker Identification**: Automatically detect who is speaking (doctor vs patient)
- **Live Conversation Display**: See conversation in real-time with speaker labels
- **SOAP Note Generation**: AI-powered medical note generation using Groq
- **Streamlit Frontend**: Clean, intuitive user interface
- **FastAPI Backend**: Fast, reliable API processing

## Tech Stack
- **Frontend**: Streamlit with real-time auto-refresh
- **Backend**: FastAPI with WebSocket support
- **Voice Processing**: Speech Recognition + Google Speech API
- **AI**: Groq API (llama3-8b-8192) for SOAP note generation
- **Speaker Detection**: Audio feature analysis using librosa
- **Audio**: gTTS for text-to-speech

## 🚀 Quick Start

### 1. Install Dependencies
```bash
cd ANother
python install_dependencies.py
```

### 2. Start Backend (Terminal 1)
```bash
python start_backend.py
```
Backend will run on: http://localhost:8001

### 3. Start Frontend (Terminal 2)
```bash
python start_frontend.py
```
Frontend will run on: http://localhost:8501

### 4. Open App
Open your browser and go to: **http://localhost:8501**

## 📋 How to Use

### Step 1: Start Listen Mode
1. Click **"🎤 Start Listen Mode"** button
2. System creates new conversation session
3. Status shows **"🔴 LIVE RECORDING"**

### Step 2: Record Conversation
1. **Doctor and Patient speak naturally** - no turn-taking needed!
2. Click **"🎤 Click to Record"** for each voice segment
3. System automatically identifies **👨‍⚕️ Doctor** vs **🤒 Patient**
4. **Real-time conversation** appears on right side with timestamps

### Step 3: Stop & Analyze
1. Click **"🛑 Stop Listen Mode"** when conversation ends
2. Enter **Patient Name** (optional)
3. Click **"📋 Generate SOAP Note"**
4. AI analyzes conversation and creates structured medical note

### Step 4: Review SOAP Note
- **S - Subjective**: Patient's reported symptoms
- **O - Objective**: Doctor's observations
- **A - Assessment**: Medical diagnosis/impression
- **P - Plan**: Treatment recommendations
- **Download** option available

## 🎯 Example Workflow

**Patient**: "Hi doctor, I've been having stomach pain for 2 days"
**Doctor**: "Can you describe the pain? Where exactly is it located?"
**Patient**: "It's in my upper abdomen, feels like burning sensation"
**Doctor**: "Any nausea or vomiting? Have you taken any medications?"
**Patient**: "Yes, some nausea but no vomiting. I took some antacids"
**Doctor**: "Based on your symptoms, this sounds like gastritis. I'll prescribe some medication"

→ **AI generates complete SOAP note** with proper medical formatting!

## 🏗️ Project Structure
```
ANother/
├── frontend/
│   ├── app.py              # Streamlit main app with real-time UI
│   └── requirements.txt    # Frontend dependencies
├── backend/
│   ├── main.py            # FastAPI server with WebSocket
│   ├── voice_processor.py # Voice & speaker detection
│   ├── soap_generator.py  # SOAP note AI logic
│   └── requirements.txt   # Backend dependencies
├── shared/
│   └── models.py          # Shared data models
├── start_backend.py       # Backend startup script
├── start_frontend.py      # Frontend startup script
├── install_dependencies.py # Dependency installer
└── README.md
```

## 🔧 Technical Details

### Speaker Detection Algorithm
- **Audio Feature Extraction**: Pitch, spectral centroid, energy, tempo
- **Classification**: Rule-based approach (can be upgraded to ML model)
- **Confidence Scoring**: Combined transcription + speaker detection confidence

### SOAP Note Generation
- **AI Model**: Groq llama3-8b-8192
- **Prompt Engineering**: Medical-specific prompts for accurate SOAP formatting
- **Structured Output**: Automatic parsing into S-O-A-P sections

### Real-time Updates
- **Auto-refresh**: Frontend refreshes every 3 seconds during recording
- **WebSocket Support**: Backend ready for real-time push notifications
- **Session Management**: Multiple conversation sessions supported

## 🎛️ API Endpoints

### Backend API (Port 8001)
- `POST /session/start` - Start new conversation session
- `POST /session/stop` - Stop active session
- `POST /voice/process` - Process audio and detect speaker
- `GET /session/{id}/conversation` - Get conversation messages
- `POST /soap/generate` - Generate SOAP note from conversation
- `GET /sessions/active` - List active sessions
- `WebSocket /ws/{session_id}` - Real-time updates

## 🔐 Configuration

### Groq API Key
Update in `backend/soap_generator.py`:
```python
self.api_key = "your_groq_api_key_here"
```

### Audio Settings
Modify in `backend/voice_processor.py`:
- Microphone calibration duration
- Speech recognition language
- Speaker detection thresholds

## 🚨 Troubleshooting

### Common Issues

**1. Microphone not working**
- Check microphone permissions
- Ensure microphone is not used by other apps
- Try restarting the backend

**2. Speaker detection inaccurate**
- Speak clearly and loudly
- Ensure good audio quality
- Consider training custom ML model for better accuracy

**3. SOAP note generation fails**
- Check Groq API key
- Verify internet connection
- Ensure conversation has sufficient content

**4. Frontend not connecting to backend**
- Verify backend is running on port 8001
- Check firewall settings
- Ensure both services are on same network

## 🎯 Future Enhancements

1. **ML-based Speaker Detection**: Train custom models for better accuracy
2. **Multi-language Support**: Support for different languages
3. **Voice Activity Detection**: Automatic start/stop recording
4. **Database Integration**: Persistent storage for conversations
5. **User Authentication**: Multi-user support with login
6. **Export Options**: PDF, Word, HL7 FHIR formats
7. **Integration**: EMR/EHR system integration
8. **Mobile App**: React Native or Flutter mobile version

## 📝 License

This project is for educational and demonstration purposes.
For production medical use, ensure compliance with HIPAA and other healthcare regulations.

---

**Built with ❤️ using Streamlit + FastAPI + Groq AI**
