# install_dependencies.py - Install all required dependencies
import subprocess
import sys
import os

def install_dependencies():
    """Install dependencies for both frontend and backend"""
    print("📦 Installing SOAP Note App Dependencies...")
    
    # Install backend dependencies
    print("\n🔧 Installing Backend Dependencies...")
    backend_req = os.path.join(os.path.dirname(__file__), 'backend', 'requirements.txt')
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", backend_req], check=True)
        print("✅ Backend dependencies installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install backend dependencies: {e}")
        return False
    
    # Install frontend dependencies
    print("\n🎨 Installing Frontend Dependencies...")
    frontend_req = os.path.join(os.path.dirname(__file__), 'frontend', 'requirements.txt')
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", frontend_req], check=True)
        print("✅ Frontend dependencies installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install frontend dependencies: {e}")
        return False
    
    print("\n🎉 All dependencies installed successfully!")
    print("\n📋 Next Steps:")
    print("1. Run: python start_backend.py (in one terminal)")
    print("2. Run: python start_frontend.py (in another terminal)")
    print("3. Open: http://localhost:8501 in your browser")
    
    return True

if __name__ == "__main__":
    install_dependencies()
