# frontend/app.py - Minimal Streamlit Frontend
import streamlit as st
import requests
import time
from audio_recorder_streamlit import audio_recorder
import io
import base64

# Page config
st.set_page_config(
    page_title="🩺 SOAP Note Voice Chat", 
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Backend URL
BACKEND_URL = "http://127.0.0.1:8000"

# Custom CSS for better UI
st.markdown("""
<style>
.patient-msg {
    background-color: #fce4ec;
    color: #2d2d2d;
    padding: 10px;
    border-radius: 10px;
    margin: 5px 0;
    border-left: 4px solid #9C27B0;
}
.doctor-msg {
    background-color: #e8f4fd;
    color: #2d2d2d;
    padding: 10px;
    border-radius: 10px;
    margin: 5px 0;
    border-left: 4px solid #2196F3;
}
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'conversation' not in st.session_state:
    st.session_state.conversation = []
if 'consultation_active' not in st.session_state:
    st.session_state.consultation_active = False

# Helper functions
def call_backend(endpoint, method="GET", data=None, files=None):
    """Call backend API"""
    try:
        url = f"{BACKEND_URL}/{endpoint}"
        if method == "POST":
            if files:
                response = requests.post(url, files=files, timeout=30)
            else:
                response = requests.post(url, json=data, timeout=30)
        else:
            response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"Backend error: {response.status_code}")
            return None
    except requests.exceptions.RequestException as e:
        st.error(f"Connection error: {str(e)}")
        return None

def transcribe_audio(audio_bytes):
    """Transcribe audio using backend"""
    files = {"audio": ("audio.wav", io.BytesIO(audio_bytes), "audio/wav")}
    result = call_backend("transcribe", "POST", files=files)
    return result.get("transcribed_text", "") if result else ""

def get_doctor_response(patient_message):
    """Get doctor response from backend"""
    result = call_backend("chat", "POST", {"message": patient_message})
    return result.get("response", "") if result else ""

def generate_soap_note():
    """Generate SOAP note from backend"""
    result = call_backend("generate_soap", "POST")
    return result.get("soap_note", "") if result else ""

def play_doctor_speech(text):
    """Get doctor speech audio from backend and play it"""
    try:
        response = requests.post(f"{BACKEND_URL}/speak", json={"message": text})
        if response.status_code == 200:
            # Create audio player with controls and autoplay
            audio_base64 = base64.b64encode(response.content).decode()

            # Create a unique key for this audio
            audio_key = f"doctor_audio_{len(st.session_state.conversation)}"

            # Display audio with prominent controls
            st.markdown("### 🔊 Doctor Response Audio")
            st.audio(response.content, format="audio/mp3")

            # Also create HTML5 audio with autoplay attempt
            audio_html = f"""
            <div style="background: #e8f4fd; padding: 15px; border-radius: 10px; margin: 10px 0; border-left: 4px solid #2196F3;">
                <p style="color: #1976D2; font-weight: bold; margin: 0 0 10px 0;">🔊 Doctor Speaking - Click Play Button Above ⬆️</p>
                <audio id="{audio_key}" controls style="width: 100%;" onloadstart="this.volume=0.8">
                    <source src="data:audio/mp3;base64,{audio_base64}" type="audio/mp3">
                    Your browser does not support the audio element.
                </audio>
                <script>
                    document.getElementById('{audio_key}').play().catch(e => console.log('Autoplay prevented:', e));
                </script>
            </div>
            """
            st.markdown(audio_html, unsafe_allow_html=True)

            # Also try direct speech on server side
            try:
                requests.post(f"{BACKEND_URL}/speak_direct", json={"message": text})
            except:
                pass  # Fallback if direct speech fails

            return True
    except Exception as e:
        st.error(f"Speech playback failed: {e}")
    return False

# Main UI
st.title("🩺 SOAP Note Voice Chat")
st.markdown("**Real-time Patient-Doctor Conversation System**")
st.markdown("---")

# Two column layout
col1, col2 = st.columns([1, 1])

with col1:
    st.subheader("🎤 Voice Interaction")
    
    # Start/Stop consultation
    if not st.session_state.consultation_active:
        if st.button("🎤 Start Consultation", type="primary", use_container_width=True):
            st.session_state.consultation_active = True
            st.session_state.conversation = []
            # Clear backend conversation
            call_backend("clear_conversation", "POST")
            st.rerun()
    else:
        st.success("🔴 Live Consultation Active")
        
        # Voice recorder
        st.markdown("**🤒 Patient - Speak Your Symptoms**")
        audio_bytes = audio_recorder(
            text="🎤 Click to Record",
            recording_color="#e74c3c",
            neutral_color="#9C27B0",
            icon_name="microphone",
            icon_size="2x",
            key=f"voice_recorder_{len(st.session_state.conversation)}"
        )
        
        # Process audio
        if audio_bytes:
            with st.spinner("🎧 Processing your voice..."):
                # Transcribe audio
                patient_text = transcribe_audio(audio_bytes)
                
                if patient_text:
                    # Get doctor response
                    doctor_response = get_doctor_response(patient_text)
                    
                    if doctor_response:
                        # Add to local conversation
                        st.session_state.conversation.append({
                            "speaker": "Patient",
                            "text": patient_text,
                            "time": time.strftime('%H:%M:%S')
                        })
                        st.session_state.conversation.append({
                            "speaker": "Doctor",
                            "text": doctor_response,
                            "time": time.strftime('%H:%M:%S')
                        })

                        # Show success message first
                        st.success("✅ Conversation updated!")

                        # Play doctor speech
                        st.info("🔊 Doctor is responding...")
                        play_doctor_speech(doctor_response)

                        # Add manual play button as backup
                        if st.button("🔊 Replay Doctor Response", key=f"replay_{len(st.session_state.conversation)}"):
                            play_doctor_speech(doctor_response)

                        st.rerun()
                    else:
                        st.error("❌ Failed to get doctor response")
                else:
                    st.error("❌ Could not understand audio. Please try again.")
        
        # Manual text input as backup
        st.markdown("**💬 Or Type Your Message**")
        manual_input = st.text_input("Type what you want to say:", key="manual_input")
        if st.button("Send Message") and manual_input:
            with st.spinner("Getting doctor response..."):
                doctor_response = get_doctor_response(manual_input)
                if doctor_response:
                    st.session_state.conversation.append({
                        "speaker": "Patient",
                        "text": manual_input,
                        "time": time.strftime('%H:%M:%S')
                    })
                    st.session_state.conversation.append({
                        "speaker": "Doctor",
                        "text": doctor_response, 
                        "time": time.strftime('%H:%M:%S')
                    })
                    st.rerun()
        
        # End consultation
        st.markdown("---")
        if st.button("🛑 End Consultation", type="secondary", use_container_width=True):
            st.session_state.consultation_active = False
            st.rerun()

with col2:
    st.subheader("💬 Live Conversation")
    
    # Display conversation
    if st.session_state.conversation:
        conversation_container = st.container()
        with conversation_container:
            for item in st.session_state.conversation:
                speaker_emoji = "👨‍⚕️" if item["speaker"] == "Doctor" else "🤒"
                time_stamp = item.get("time", "")
                
                if item["speaker"] == "Doctor":
                    st.markdown(f"""
                    <div class="doctor-msg">
                        <strong style="color: #1976D2;">{speaker_emoji} Doctor [{time_stamp}]:</strong><br>
                        {item['text']}
                    </div>
                    """, unsafe_allow_html=True)
                else:
                    st.markdown(f"""
                    <div class="patient-msg">
                        <strong style="color: #7B1FA2;">{speaker_emoji} Patient [{time_stamp}]:</strong><br>
                        {item['text']}
                    </div>
                    """, unsafe_allow_html=True)
        
        # Generate SOAP note
        st.markdown("---")
        st.subheader("📋 SOAP Note")
        if st.button("📋 Generate SOAP Note", use_container_width=True):
            with st.spinner("Generating SOAP note..."):
                soap_note = generate_soap_note()
                if soap_note:
                    st.text_area("SOAP Note", value=soap_note, height=300, key="soap_display")
                else:
                    st.error("Failed to generate SOAP note")
    
    else:
        st.info("🎤 Start consultation to see live conversation here!")

# Footer
st.markdown("---")

